const fs = require("fs");

function generateClusters(numClusters, numDataPoints) {
  const clusters = [];

  for (let i = 0; i < numClusters; i++) {
    const clusterCoordinates = `(${getBasicRandomInt(
      -20,
      20
    )},${getBasicRandomInt(-20, 20)},${getBasicRandomInt(-20, 20)})`;
    const clusterColor = [
      getBasicRandomInt(0, 255),
      getBasicRandomInt(0, 255),
      getBasicRandomInt(0, 255),
    ];
    const clusterSize = getBasicRandomInt(10, 30);
    const clusterVisible = true;
    const dataPoints = [];

    for (let j = 0; j < numDataPoints; j++) {
      const dataPointCoordinates = `(${getRandomInt(-1, 1)},${getRandomInt(
        -1,
        1
      )},${getRandomInt(-1, 1)})`;
      const dataPointSize = getRandomInt(1, 5);
      const dataPointVisible = true;

      dataPoints.push({
        coordinates: dataPointCoordinates,
        color: `rgb(${clusterColor[0]},${clusterColor[1]},${clusterColor[2]})`,
        size: dataPointSize,
        visible: dataPointVisible,
      });
    }

    clusters.push({
      coordinates: clusterCoordinates,
      color: clusterColor,
      size: clusterSize,
      visible: clusterVisible,
      dataPoints: dataPoints,
    });
  }

  return clusters;
}

function getBasicRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomInt() {
  return Math.floor(Math.random() * 201) / 100;
}

function writeClustersToFile(clusters) {
  let fileContents = `<init steps="1">

<step 1>
`;

  for (const cluster of clusters) {
    fileContents += `<point>\n`;
    fileContents += `\tpoint="${cluster.coordinates}"\n`;
    fileContents += `\tcolor="rgb(${cluster.color[0]},${cluster.color[1]},${cluster.color[2]})"\n`;
    fileContents += `\tsize="${cluster.size}"\n`;
    fileContents += `\tvisible="${cluster.visible}"\n`;
    fileContents += `</point>\n`;

    for (const dataPoint of cluster.dataPoints) {
      fileContents += `<point>\n`;
      fileContents += `\tpoint="${dataPoint.coordinates}"\n`;
      fileContents += `\tcolor="${dataPoint.color}"\n`;
      fileContents += `\tsize="${dataPoint.size}"\n`;
      fileContents += `\tvisible="${dataPoint.visible}"\n`;
      fileContents += `</point>\n`;
    }
  }
  fileContents += `	<window>
		hsrmode="3"
		nomidpts="true"
		anaglyph="-1"
		transparent="false"
		alpha="140"
		twoViews="false"
		unlinkViews="false"
		axisExtension="0.7"
		showNormals="false"
		showNormalsAtPts="false"
		xaxislabel="x"
		yaxislabel="y"
		zaxislabel="z"
		xmin="-2"
		xmax="2"
		xscale="1"
		xscalefactor="1"
		ymin="-2"
		ymax="2"
		yscale="1"
		yscalefactor="1"
		zmin="-2"
		zmax="2"
		zscale="1"
		zscalefactor="1"
		zminClip="-4"
		zmaxClip="4"
		edgesOn="true"
		facesOn="true"
		showBox="true"
		showAxes="true"
		showTicks="true"
		perspective="true"
		centerxpercent="0.5"
		centerypercent="0.5"
		rotationsteps="30"
		autospin="true"
		xygrid="false"
		yzgrid="false"
		xzgrid="false"
		gridsOnBox="true"
		gridPlanes="false"
		gridColor="rgb(128,128,128)"
		traceMode="0"
		keep2D="false"
		zoom="1.804"
	</window>
	<viewpoint>
		center="8.236391035463319,4.755282581475766,3.0901699437494745,1"
		focus="0,0,0,1"
		up="0,0,2,1"
	</viewpoint>
</step>`;

  fs.writeFile("clusters.txt", fileContents, (err) => {
    if (err) {
      console.error(err);
      return;
    }
    console.log("Clusters written to file.");
  });
}

writeClustersToFile(generateClusters(10, 1000));
