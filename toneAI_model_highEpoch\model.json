{"modelTopology": {"class_name": "Sequential", "config": {"name": "sequential_1", "layers": [{"class_name": "<PERSON><PERSON>", "config": {"units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_Dense1", "trainable": true, "batch_input_shape": [null, 48], "dtype": "float32"}}, {"class_name": "<PERSON><PERSON>", "config": {"units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_Dense2", "trainable": true}}, {"class_name": "<PERSON><PERSON>", "config": {"units": 4, "activation": "softmax", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_Dense3", "trainable": true}}]}, "keras_version": "tfjs-layers 4.1.0", "backend": "tensor_flow.js"}, "weightsManifest": [{"paths": ["weights.bin"], "weights": [{"name": "dense_Dense1/kernel", "shape": [48, 64], "dtype": "float32"}, {"name": "dense_Dense1/bias", "shape": [64], "dtype": "float32"}, {"name": "dense_Dense2/kernel", "shape": [64, 64], "dtype": "float32"}, {"name": "dense_Dense2/bias", "shape": [64], "dtype": "float32"}, {"name": "dense_Dense3/kernel", "shape": [64, 4], "dtype": "float32"}, {"name": "dense_Dense3/bias", "shape": [4], "dtype": "float32"}]}], "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v4.1.0", "convertedBy": null}