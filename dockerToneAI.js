const fs = require("fs-extra");
const fft = require("fft-js").fft;
const fftUtil = require("fft-js").util;
const tf = require("@tensorflow/tfjs-node-gpu");
var admin = require("firebase-admin");

console.log(tf.getBackend());

const logdir = "logs";
const summaryWriter = tf.node.summaryFileWriter(logdir);

// Fetch the service account key JSON file contents
var serviceAccount = require("./isistr-db-firebase-adminsdk-gzboz-fecbf1a908.json");

// Initialize the app with a service account, granting admin privileges
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // The database URL depends on the location of the database
  databaseURL: "https://isistr-db-default-rtdb.firebaseio.com/",
});

// As an admin, the app has access to read and write all data, regardless of Security Rules
var db = admin.database();
var ref = db.ref("restricted_access/secret_document");
ref.once("value", function (snapshot) {
  console.log(snapshot.val());
});

function getDataFromDB(datasetName) {
  switch (datasetName) {
    case "results":
      var ref = db.ref("app/results");
      break;
    case "toneAI_Results":
      var ref = db.ref("app/toneAI_Results");
      break;
    case "toneAI_Results1":
      var ref = db.ref("app/toneAI_Results1");
      break;
    case "results_new/toneAI_begin":
      var ref = db.ref("app/results_new/toneAI_begin");
      break;
    default:
      var ref = db.ref("app/results");
      break;
  }

  ref.once("value", function (snapshot) {
    const data = snapshot.val();
    if (datasetName.includes("toneAI")) {
      trainAudioModel_Dynamic(data, datasetName);
    } else {
      trainModel(data);
    }
    return data;
  });
}

async function trainAudioModel_Dynamic(data, identifier) {
  console.log(data, "data");
  console.log(identifier, "identifier");

  let resultsArray = [];

  Object.keys(data).forEach((key) => {
    for (let i = 0; i < data[key].length; i++) {
      resultsArray.push(data[key][i]);
    }
  });

  const numClasses = 4;

  if (!Array.isArray(resultsArray)) {
    console.error("Results is not an array.");
  }

  function applyFFT(inputTensor) {
    const fftOutput = [];

    for (let i = 0; i < 12; i++) {
      const signal = inputTensor.slice([i], [1]);
      const signalLength = signal.shape[0];
      const signalLengthTensor = tf.scalar(signalLength);

      // Apply FFT to the signal
      const signalFFT = tf.spectral.rfft(signal);

      // Compute the power spectrum of the signal
      const powerSpectrum = tf.abs(signalFFT).square().div(signalLengthTensor);

      // Convert power spectrum to a regular array
      const powerSpectrumData = powerSpectrum.dataSync();

      // Store the power spectrum for this signal in the output array
      fftOutput.push(powerSpectrumData);

      // Dispose of the tensors we created
      signal.dispose(); // Needed to avoid memory leak
    }

    return fftOutput;
  }

  const newResultsArray = resultsArray.map(([result, label], index) => {
    const tempArray = [];

    // loop the result array and split and create 4 new arrays of 12 each
    // resultsArray.length
    if (index < resultsArray.length) {
      for (let j = 0; j < result.length; j += 12) {
        const newResult = tf.tensor1d(result.slice(j, j + 12), "float32");
        const fftResult = applyFFT(newResult);
        // console.log("Electrode processed"); //  spam output to console
        tempArray.push(fftResult);
      }
    }

    return [tempArray, label];
  });

  console.log("Finished processing data.");

  const newData = newResultsArray.map(([tempArray, label]) => {
    const arr = [];
    for (let i = 0; i < tempArray.length; i++) {
      const subArr = tempArray[i].map((item) => [...item]);
      while (subArr.length < 12) {
        subArr.push(new Array(12).fill(0));
      }
      arr.push(subArr);
    }
    return [arr, label];
  });

  //   const { features, labels } = newResultsArray.map(([tempArray, label]) => {
  //     const arr = [];
  //     for (let i = 0; i < tempArray.length; i++) {
  //       const subArr = tempArray[i].map((item) => [...item]);
  //       while (subArr.length < 12) {
  //         subArr.push(new Array(12).fill(0));
  //       }
  //       arr.push(subArr);
  //     }
  //     return { features: arr, labels: label };
  //   });

  //   const dataset = tf.data.array(newData);

  //   const dataset = tf.data
  //     .array([featuresTensor, labelsTensor])
  //     .map(([features, label]) => {
  //       // Convert features to the shape that the model expects
  //       const reshapedFeatures = tf.reshape(features, [-1, 4, 12, 1]);
  //       return { xs: reshapedFeatures, ys: label };
  //     })
  //     .batch(32);

  // Output the shape of each element in the dataset
  //   dataset.forEachAsync((element) => {
  //     console.log(element.shape); // output: [3, 4]
  //     return Promise.resolve(); // required to avoid warning message
  //   }, this);

  // Get first entry as a tensor
  //   const firstEntry = await dataset.take(1);

  // Convert array to tensor
  //   const tensor = tf.data.Dataset.toTensor(firstEntry);

  // Get shape of tensor
  //   console.log("Shape:", tensor.shape); // Shape: [1, 4]

  // Output the first element in the dataset
  // console.log(newData[0], "newData");
  // console.log(newData[0][0], "newData");
  // console.log(newData[0][0][0], "newData");

  const sample = newData[0];
  //   const tensor = tf.tensor(sample);

  // Define the input shape
  const inputShape = [4, 12, 1];
  const numExamples = newData.length;
  const numTrainExamples = Math.floor(numExamples * 0.7);
  const numValExamples = Math.floor(numExamples * 0.15);
  const numTestExamples = numExamples - numTrainExamples - numValExamples;
  const batchSize = 32;

  // create features and labels arrays
  const features = [];
  const labels = [];

  // loop through the data and push the features and labels into their respective arrays
  for (let i = 0; i < newData.length; i++) {
    if (newData[i][0].length < 4) {
      continue;
    }
    // features.push(newData[i][0]);
    // one hot encode the labels from numClasses
    const label = tf.oneHot(newData[i][1], numClasses);
    const feature = tf.tensor(newData[i][0], [4, 12, 1]);

    labels.push(label);
    features.push(feature);

    if (i % 5 === 0) {
      console.log(`Data: ${features.length} / ${newData.length}`);
      console.log(`Feature: ${features[features.length - 1]}`);
      console.log(`Label: ${labels[labels.length - 1]}`);
    }
  }

  const test = [];

  const dataset = tf.data.array({ xs: features, ys: labels });

  // await dataset.forEachAsync((e) => {
  //   test.push(e);
  // });

  // Shuffle the data and split into training, validation, and test sets
  const trainDataset = dataset.take(numTrainExamples).batch(batchSize);
  const valDataset = dataset
    .skip(numTrainExamples)
    .take(numValExamples)
    .batch(batchSize);
  const testDataset = dataset
    .skip(numTrainExamples + numValExamples)
    .batch(batchSize);

  // await dataset.forEachAsync((e) => {
  //   test.push(e);
  // });

  //   simple test model that takes in the dataset

  // const model = tf.sequential({
  //   layers: [
  //     tf.layers.dense({
  //       // Hyperparameter for the model. EEG dsata is 4x12x1 (4 electrodes, 12 time steps, 1 channel)
  //       // 128 units in the first layer is a good starting point due to the number of electrodes and their spatial relationship
  //       units: 48,
  //       // Recitified Linear Unit (ReLU) is a good starting point for the activation function due to its simplicity and performance
  //       activation: "relu",
  //       // Input shape is 4 electrodes, 12 time steps, 1 channel
  //       inputShape: [1, 4, 12],
  //     }),
  //     // Enable dropout regularization to prevent overfitting. 0.2 is a good starting point
  //     // tf.layers.dropout({ rate: 0.2 }),

  //     // Enable max pooling to reduce the number of parameters in the model, which will help prevent overfitting
  //     // tf.layers.maxPooling2d({ poolSize: [2, 2], strides: [2, 2] }),

  //     // Flatten the output from the 2D filters into a 1D vector to prepare it for input into our last layer
  //     tf.layers.flatten(),

  //     tf.layers.dense({ units: 64, activation: "relu" }),
  //     // Output layer. 4 units for the 4 classes and softmax for probability distribution
  //     tf.layers.dense({ units: numClasses, activation: "softmax" }),
  //   ],
  // });

  const model = tf.sequential({
    layers: [
      tf.layers.dense({
        activation: "relu",
        inputShape: [4, 12, 1, features.length],
      }),

      // Flatten the output from the dense layer into a 1D vector to prepare it for input into our last layer
      // tf.layers.flatten(),

      // Output layer. 4 units for the 4 classes and softmax for probability distribution
      tf.layers.dense({ units: numClasses, activation: "softmax" }),
    ],
  });

  model.compile({
    optimizer: "adam",
    loss: "categoricalCrossentropy",
    metrics: ["accuracy"],
  });

  // Set up TensorBoard callback
  const tensorBoardCallback = tf.node.tensorBoard(logdir, {
    updateFreq: "epoch",
    histogramFreq: 1,
  });

  console.log(`Features: ${features[0].shape}`);
  console.log(`Labels: ${labels[0].shape}`);

  // Train the model on the reshaped data using trainDataset
  model.fitDataset(trainDataset, {
    epochs: 10,
    validationData: valDataset,
    callbacks: [tensorBoardCallback],
  });

  // // model.fit(features, labels, {
  // await model.fit(features, labels, {
  //   epochs: 10,
  //   validationData: valDataset,
  //   callbacks: [tensorBoardCallback],
  // });

  // Train the model on the reshaped data
  // await model.fit(trainDataset, {
  //   epochs: 10,
  //   validationData: valDataset,
  //   callbacks: [tensorBoardCallback],
  // });

  // Evaluate the model on the test dataset
  const evalOutput = model.evaluate(testDataset);

  // Log the evaluation accuracy
  console.log(`Test Accuracy: ${(await evalOutput[1].data())[0]}`);

  // Save the model
  await model.save("file://./model");
}

getDataFromDB("results_new/toneAI_begin");
