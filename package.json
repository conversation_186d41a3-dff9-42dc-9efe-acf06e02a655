{"name": "eeg<PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "author": "LiveBacteria - (<PERSON>)", "license": "ISC", "dependencies": {"@tensorflow/tfjs": "^4.1.0", "@tensorflow/tfjs-node": "^4.2.0", "@tensorflow/tfjs-node-gpu": "^4.1.0", "@tensorflow/tfjs-vis": "^1.5.1", "axios": "^1.2.1", "body-parser": "^1.20.1", "cors": "^2.8.5", "csv-parser": "^3.0.0", "express": "^4.18.2", "fft-js": "^0.0.12", "firebase-admin": "^11.3.0", "fs-extra": "^11.1.0", "nodemon": "^2.0.20", "papaparse": "^5.3.2", "progress": "^2.0.3", "ws": "^8.12.1"}}