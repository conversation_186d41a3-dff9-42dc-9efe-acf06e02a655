const tf = require("@tensorflow/tfjs-node-gpu");

async function trainModel() {
  const arr = [
    [
      0, 0, 3.814697265625, 8.58306884765625, 362.6346435546875,
      2384.185791015625, 3433.2275390625, 2432.10791015625, 1722.57421875,
      3208.160400390625, 3208.160400390625, 2243.280517578125, 0,
      0.2384185791015625, 2.1457672119140625, 8.58306884765625, 326.39501953125,
      1888.5135498046875, 1763.34375, 1413.583740234375, 1270.5325927734375,
      1235.9619140625, 887.155517578125, 1270.5325927734375, 0.2384185791015625,
      0, 0, 0.95367431640625, 15.2587890625, 86.06910705566406, 95.367431640625,
      149.01161193847656, 46.73004150390625, 8.58306884765625,
      28.848648071289062, 34.332275390625, 0, 0, 0.95367431640625,
      2.1457672119140625, 115.39459228515625, 1007.3184814453125, 1525.87890625,
      858.306884765625, 829.93505859375, 2062.082275390625, 2529.3828125,
      2062.082275390625,
    ],
    [
      0, 0, 3.814697265625, 8.58306884765625, 362.6346435546875,
      2384.185791015625, 3433.2275390625, 2432.10791015625, 1722.57421875,
      3208.160400390625, 3208.160400390625, 2243.280517578125, 0,
      0.2384185791015625, 2.1457672119140625, 8.58306884765625, 326.39501953125,
      1888.5135498046875, 1763.34375, 1413.583740234375, 1270.5325927734375,
      1235.9619140625, 887.155517578125, 1270.5325927734375, 0.2384185791015625,
      0, 0, 0.95367431640625, 15.2587890625, 86.06910705566406, 95.367431640625,
      149.01161193847656, 46.73004150390625, 8.58306884765625,
      28.848648071289062, 34.332275390625, 0, 0, 0.95367431640625,
      2.1457672119140625, 115.39459228515625, 1007.3184814453125, 1525.87890625,
      858.306884765625, 829.93505859375, 2062.082275390625, 2529.3828125,
      2062.082275390625,
    ],
  ];

  const features = tf.data.array(arr);
  const labels = [];
  const oneHot = tf.oneHot(0, 4);
  labels.push(oneHot);

  const labelsTensor = tf.stack(labels);
  //   const featuresTensor = tf.stack(features);

  const model = tf.sequential();
  model.add(tf.layers.dense({ units: 16, inputShape: 48, activation: "relu" }));
  model.add(tf.layers.dense({ units: 8, activation: "relu" }));
  model.add(tf.layers.dense({ units: 1, activation: "sigmoid" }));

  model.compile({
    optimizer: "adam",
    loss: "binaryCrossentropy",
    metrics: ["accuracy"],
  });

  const tensorBoardCallback = tf.node.tensorBoard("logs", {
    updateFreq: "epoch",
    histogramFreq: 1,
  });

  // Reshape the input tensor to have shape [*,12]

  await model.fit(features, labels, {
    epochs: 10,
    callbacks: [tensorBoardCallback],
  });
  const evalOutput = model.evaluate(normalizedArr, labels);
  console.log(`Test Accuracy: ${(await evalOutput[1].data())[0]}`);
}

trainModel();
